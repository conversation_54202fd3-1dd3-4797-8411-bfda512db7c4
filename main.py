


















                                     f"Chyba pri exporte: {str(e)}")

    def save_project(self):
        """Save current project to file"""
        if not self.points:
            messagebox.showwarning("Upozornenie", "Nie je čo uložiť.")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Uložiť projekt"
        )

        if filename:
            try:
                project_data = {
                    'version': '1.0',
                    'timestamp': datetime.now().isoformat(),
                    'address': self.address_var.get(),
                    'coordinates': {
                        'lat': self.current_lat,
                        'lng': self.current_lng
                    },
                    'map_settings': {
                        'zoom': self.current_zoom,
                        'map_type': self.map_type
                    },
                    'points': [{'x': p.x, 'y': p.y} for p in self.points],
                    'meters_per_pixel': self.meters_per_pixel
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("Úspech", f"Projekt bol uložený do súboru:\n{filename}")

            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri ukladaní: {str(e)}")

    def load_project(self):
        """Load project from file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Načítať projekt"
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # Restore project data
                if 'address' in project_data:
                    self.address_var.set(project_data['address'])

                if 'coordinates' in project_data:
                    coords = project_data['coordinates']
                    self.current_lat = coords['lat']
                    self.current_lng = coords['lng']

                if 'map_settings' in project_data:
                    settings = project_data['map_settings']
                    self.current_zoom = settings.get('zoom', 20)
                    self.map_type = settings.get('map_type', 'satellite')
                    self.zoom_var.set(self.current_zoom)
                    self.map_type_var.set(self.map_type)

                if 'meters_per_pixel' in project_data:
                    self.meters_per_pixel = project_data['meters_per_pixel']

                # Restore points
                if 'points' in project_data:
                    self.points = [Point(p['x'], p['y']) for p in project_data['points']]

                # Reload map and redraw
                if self.current_lat and self.current_lng:
                    self.load_satellite_image_from_coords(self.current_lat, self.current_lng)

                messagebox.showinfo("Úspech", f"Projekt bol načítaný zo súboru:\n{filename}")

            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri načítavaní: {str(e)}")

    def show_about(self):
        """Show about dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("O aplikácii")
        dialog.geometry("400x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        tk.Label(dialog, text="Analyzátor rozmerov strechy",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=20)

        tk.Label(dialog, text="Verzia 2.0",
                font=('Arial', 12), bg='#f3f4f6').pack(pady=5)

        tk.Label(dialog, text="Profesionálny nástroj na analýzu rozmerov striech\npoužívajúci Google Maps API",
                font=('Arial', 10), bg='#f3f4f6', justify=tk.CENTER).pack(pady=10)

        tk.Label(dialog, text="Funkcie:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(20, 5))

        features = [
            "• Reálne satelitné mapy z Google Maps",
            "• Automatická detekcia striech",
            "• Presné meranie plôch a vzdialeností",
            "• Export výsledkov",
            "• Ukladanie a načítavanie projektov"
        ]

        for feature in features:
            tk.Label(dialog, text=feature, font=('Arial', 9), bg='#f3f4f6', anchor='w').pack(pady=1)

        tk.Button(dialog, text="Zavrieť", command=dialog.destroy,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=20)

def main():
    root = tk.Tk()
    app = RoofAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()




