import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import math
import requests
import json
from PIL import Image, ImageTk, ImageDraw
import io
import urllib.parse
import threading
from typing import List, Tuple, Optional
import os
from datetime import datetime
import numpy as np

# Try to import OpenCV and other advanced libraries
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")

try:
    from skimage import filters, segmentation, measure, morphology
    from skimage.feature import canny
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False
    print("scikit-image nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("matplotlib nie je nainštalované. Vizualizácia kontúr nebude dostupná.")

class Point:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y

class RoofAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Analyzátor rozmerov strechy - Google Maps")
        self.root.geometry("1200x900")
        self.root.configure(bg='#f3f4f6')
        
        # Application state
        self.points: List[Point] = []
        self.canvas_width = 800
        self.canvas_height = 600
        self.meters_per_pixel = 0.05
        self.current_image = None
        self.photo_image = None
        self.current_lat = None
        self.current_lng = None
        self.current_zoom = 20
        self.map_type = "satellite"
        
        # Detection and contour data
        self.detected_contours = []
        self.show_contours = tk.BooleanVar(value=True)
        self.show_edges = tk.BooleanVar(value=False)
        self.processed_image = None
        self.contour_overlay = None
        
        # Google Maps API key - MUSÍTE NAHRADIŤ VLASTNÝM KĽÚČOM
        self.api_key = self.load_api_key()
        
        # Geocoding cache
        self.geocoding_cache = {}
        
        self.setup_ui()
        self.check_api_key()
    
    def load_api_key(self):
        """Load Google Maps API key from file or environment"""
        # Try to load from api_key.txt file
        if os.path.exists("api_key.txt"):
            with open("api_key.txt", "r") as f:
                key = f.read().strip()
                if key:
                    return key
        
        # Try environment variable
        key = os.environ.get("GOOGLE_MAPS_API_KEY")
        if key:
            return key
            
        return None
    
    def check_api_key(self):
        """Check if API key is configured"""
        if not self.api_key:
            response = messagebox.askyesno(
                "Google Maps API Key",
                "Google Maps API kľúč nie je nakonfigurovaný.\n\n"
                "Chcete zadať API kľúč teraz?\n\n"
                "Bez API kľúča aplikácia nebude fungovať správne."
            )
            if response:
                self.configure_api_key()
    
    def configure_api_key(self):
        """Configure Google Maps API key"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Konfigurácia Google Maps API")
        dialog.geometry("500x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))
        
        tk.Label(dialog, text="Google Maps API Konfigurácia", 
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)
        
        tk.Label(dialog, text="Pre použitie tejto aplikácie potrebujete Google Maps API kľúč.",
                font=('Arial', 10), bg='#f3f4f6', wraplength=450).pack(pady=5)
        
        tk.Label(dialog, text="1. Idite na: https://console.cloud.google.com/",
                font=('Arial', 9), bg='#f3f4f6', fg='blue').pack(pady=2)
        
        tk.Label(dialog, text="2. Vytvorte nový projekt alebo vyberte existujúci",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)
        
        tk.Label(dialog, text="3. Povoľte Maps Static API a Geocoding API",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)
        
        tk.Label(dialog, text="4. Vytvorte API kľúč v sekcii 'Credentials'",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)
        
        tk.Label(dialog, text="Zadajte váš Google Maps API kľúč:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(20, 5))
        
        api_entry = tk.Entry(dialog, width=60, font=('Arial', 10))
        api_entry.pack(pady=5)
        
        def save_key():
            key = api_entry.get().strip()
            if key:
                self.api_key = key
                # Save to file
                with open("api_key.txt", "w") as f:
                    f.write(key)
                messagebox.showinfo("Úspech", "API kľúč bol uložený!")
                dialog.destroy()
            else:
                messagebox.showwarning("Chyba", "Prosím zadajte platný API kľúč.")
        
        tk.Button(dialog, text="Uložiť", command=save_key,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=10)
        
        tk.Button(dialog, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 11),
                 padx=20, pady=5).pack(pady=5)

    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f3f4f6', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="Analyzátor rozmerov strechy", 
                              font=('Arial', 24, 'bold'), bg='#f3f4f6', fg='#1f2937')
        title_label.pack(pady=(0, 10))
        
        subtitle_label = tk.Label(main_frame, 
                                 text="Vyhľadajte adresu a potom obrys vyznačte manuálne alebo použite automatickú analýzu.",
                                 font=('Arial', 12), bg='#f3f4f6', fg='#6b7280')
        subtitle_label.pack(pady=(0, 20))
        
        # Search frame
        search_frame = tk.Frame(main_frame, bg='#f3f4f6')
        search_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Address input
        address_frame = tk.Frame(search_frame, bg='#f3f4f6')
        address_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.address_var = tk.StringVar()
        self.address_entry = tk.Entry(address_frame, textvariable=self.address_var, 
                                     font=('Arial', 12), width=40)
        self.address_entry.pack(side=tk.LEFT, padx=(0, 10), ipady=5)
        self.address_entry.insert(0, "Zadajte adresu (napr. Bratislava, Slovensko)")
        
        search_btn = tk.Button(address_frame, text="Hľadať", command=self.handle_search,
                              bg='#2563eb', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=5)
        search_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Map controls
        controls_frame = tk.Frame(search_frame, bg='#f3f4f6')
        controls_frame.pack(fill=tk.X)
        
        tk.Label(controls_frame, text="Zoom:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))
        
        self.zoom_var = tk.IntVar(value=20)
        zoom_scale = tk.Scale(controls_frame, from_=15, to=22, orient=tk.HORIZONTAL, 
                             variable=self.zoom_var, command=self.on_zoom_change,
                             bg='#f3f4f6', length=100)
        zoom_scale.pack(side=tk.LEFT, padx=(0, 20))
        
        tk.Label(controls_frame, text="Typ mapy:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))
        
        self.map_type_var = tk.StringVar(value="satellite")
        map_type_combo = ttk.Combobox(controls_frame, textvariable=self.map_type_var, 
                                     values=["satellite", "hybrid", "roadmap"], 
                                     state="readonly", width=10)
        map_type_combo.pack(side=tk.LEFT, padx=(0, 20))
        map_type_combo.bind('<<ComboboxSelected>>', self.on_map_type_change)
        
        refresh_btn = tk.Button(controls_frame, text="Obnoviť mapu", command=self.refresh_map,
                               bg='#059669', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        refresh_btn.pack(side=tk.LEFT)
        
        # Detection controls
        detection_frame = tk.Frame(search_frame, bg='#f3f4f6')
        detection_frame.pack(fill=tk.X, pady=(10, 0))
        
        tk.Label(detection_frame, text="Zobrazenie:", font=('Arial', 10, 'bold'), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 10))
        
        contours_check = tk.Checkbutton(detection_frame, text="Kontúry", variable=self.show_contours,
                                       command=self.toggle_contours, bg='#f3f4f6', font=('Arial', 9))
        contours_check.pack(side=tk.LEFT, padx=(0, 10))
        
        edges_check = tk.Checkbutton(detection_frame, text="Hrany", variable=self.show_edges,
                                    command=self.toggle_edges, bg='#f3f4f6', font=('Arial', 9))
        edges_check.pack(side=tk.LEFT, padx=(0, 10))
        
        detect_btn = tk.Button(detection_frame, text="Detekcia striech", command=self.detect_roofs,
                              bg='#7c3aed', fg='white', font=('Arial', 10, 'bold'),
                              padx=15, pady=3)
        detect_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        contour_btn = tk.Button(detection_frame, text="Zobraziť kontúry", command=self.show_contour_analysis,
                               bg='#0891b2', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        contour_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        fine_tune_btn = tk.Button(detection_frame, text="Doladiť detekciu", command=self.show_fine_tune_dialog,
                                 bg='#8b5cf6', fg='white', font=('Arial', 10, 'bold'),
                                 padx=15, pady=3)
        fine_tune_btn.pack(side=tk.LEFT, padx=(10, 0))
