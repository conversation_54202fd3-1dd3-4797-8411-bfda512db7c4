import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import math
import requests
import json
from PIL import Image, ImageTk, ImageDraw
import io
import urllib.parse
import threading
from typing import List, Tuple, Optional
import os
from datetime import datetime
import numpy as np

# Try to import OpenCV and other advanced libraries
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")

try:
    from skimage import filters, segmentation, measure, morphology
    from skimage.feature import canny
    SKIMAGE_AVAILABLE = True
except ImportError:
    SKIMAGE_AVAILABLE = False
    print("scikit-image nie je nainštalované. Niektoré pokročilé funkcie nebudú dostupné.")

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("matplotlib nie je nainštalované. Vizualizácia kontúr nebude dostupná.")

class Point:
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y

class RoofAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Analyzátor rozmerov strechy - Google Maps")
        self.root.geometry("1200x900")
        self.root.configure(bg='#f3f4f6')

        # Application state
        self.points: List[Point] = []
        self.canvas_width = 800
        self.canvas_height = 600
        self.meters_per_pixel = 0.05
        self.current_image = None
        self.photo_image = None
        self.current_lat = None
        self.current_lng = None
        self.current_zoom = 20
        self.map_type = "satellite"

        # Detection and contour data
        self.detected_contours = []
        self.show_contours = tk.BooleanVar(value=True)
        self.show_edges = tk.BooleanVar(value=False)
        self.processed_image = None
        self.contour_overlay = None

        # Google Maps API key - MUSÍTE NAHRADIŤ VLASTNÝM KĽÚČOM
        self.api_key = self.load_api_key()

        # Geocoding cache
        self.geocoding_cache = {}

        self.setup_ui()
        self.check_api_key()

    def load_api_key(self):
        """Load Google Maps API key from file or environment"""
        # Try to load from api_key.txt file
        if os.path.exists("api_key.txt"):
            with open("api_key.txt", "r") as f:
                key = f.read().strip()
                if key:
                    return key

        # Try environment variable
        key = os.environ.get("GOOGLE_MAPS_API_KEY")
        if key:
            return key

        return None

    def check_api_key(self):
        """Check if API key is configured"""
        if not self.api_key:
            response = messagebox.askyesno(
                "Google Maps API Key",
                "Google Maps API kľúč nie je nakonfigurovaný.\n\n"
                "Chcete zadať API kľúč teraz?\n\n"
                "Bez API kľúča aplikácia nebude fungovať správne."
            )
            if response:
                self.configure_api_key()

    def configure_api_key(self):
        """Configure Google Maps API key"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Konfigurácia Google Maps API")
        dialog.geometry("500x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        tk.Label(dialog, text="Google Maps API Konfigurácia",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)

        tk.Label(dialog, text="Pre použitie tejto aplikácie potrebujete Google Maps API kľúč.",
                font=('Arial', 10), bg='#f3f4f6', wraplength=450).pack(pady=5)

        tk.Label(dialog, text="1. Idite na: https://console.cloud.google.com/",
                font=('Arial', 9), bg='#f3f4f6', fg='blue').pack(pady=2)

        tk.Label(dialog, text="2. Vytvorte nový projekt alebo vyberte existujúci",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="3. Povoľte Maps Static API a Geocoding API",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="4. Vytvorte API kľúč v sekcii 'Credentials'",
                font=('Arial', 9), bg='#f3f4f6').pack(pady=2)

        tk.Label(dialog, text="Zadajte váš Google Maps API kľúč:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(20, 5))

        api_entry = tk.Entry(dialog, width=60, font=('Arial', 10))
        api_entry.pack(pady=5)

        def save_key():
            key = api_entry.get().strip()
            if key:
                self.api_key = key
                # Save to file
                with open("api_key.txt", "w") as f:
                    f.write(key)
                messagebox.showinfo("Úspech", "API kľúč bol uložený!")
                dialog.destroy()
            else:
                messagebox.showwarning("Chyba", "Prosím zadajte platný API kľúč.")

        tk.Button(dialog, text="Uložiť", command=save_key,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=10)

        tk.Button(dialog, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 11),
                 padx=20, pady=5).pack(pady=5)

    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg='#f3f4f6', padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = tk.Label(main_frame, text="Analyzátor rozmerov strechy",
                              font=('Arial', 24, 'bold'), bg='#f3f4f6', fg='#1f2937')
        title_label.pack(pady=(0, 10))

        subtitle_label = tk.Label(main_frame,
                                 text="Vyhľadajte adresu a potom obrys vyznačte manuálne alebo použite automatickú analýzu.",
                                 font=('Arial', 12), bg='#f3f4f6', fg='#6b7280')
        subtitle_label.pack(pady=(0, 20))

        # Search frame
        search_frame = tk.Frame(main_frame, bg='#f3f4f6')
        search_frame.pack(fill=tk.X, pady=(0, 20))

        # Address input
        address_frame = tk.Frame(search_frame, bg='#f3f4f6')
        address_frame.pack(fill=tk.X, pady=(0, 10))

        self.address_var = tk.StringVar()
        self.address_entry = tk.Entry(address_frame, textvariable=self.address_var,
                                     font=('Arial', 12), width=40)
        self.address_entry.pack(side=tk.LEFT, padx=(0, 10), ipady=5)
        self.address_entry.insert(0, "Zadajte adresu (napr. Bratislava, Slovensko)")

        search_btn = tk.Button(address_frame, text="Hľadať", command=self.handle_search,
                              bg='#2563eb', fg='white', font=('Arial', 12, 'bold'),
                              padx=20, pady=5)
        search_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Map controls
        controls_frame = tk.Frame(search_frame, bg='#f3f4f6')
        controls_frame.pack(fill=tk.X)

        tk.Label(controls_frame, text="Zoom:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))

        self.zoom_var = tk.IntVar(value=20)
        zoom_scale = tk.Scale(controls_frame, from_=15, to=22, orient=tk.HORIZONTAL,
                             variable=self.zoom_var, command=self.on_zoom_change,
                             bg='#f3f4f6', length=100)
        zoom_scale.pack(side=tk.LEFT, padx=(0, 20))

        tk.Label(controls_frame, text="Typ mapy:", font=('Arial', 10), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 5))

        self.map_type_var = tk.StringVar(value="satellite")
        map_type_combo = ttk.Combobox(controls_frame, textvariable=self.map_type_var,
                                     values=["satellite", "hybrid", "roadmap"],
                                     state="readonly", width=10)
        map_type_combo.pack(side=tk.LEFT, padx=(0, 20))
        map_type_combo.bind('<<ComboboxSelected>>', self.on_map_type_change)

        refresh_btn = tk.Button(controls_frame, text="Obnoviť mapu", command=self.refresh_map,
                               bg='#059669', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        refresh_btn.pack(side=tk.LEFT)

        # Detection controls
        detection_frame = tk.Frame(search_frame, bg='#f3f4f6')
        detection_frame.pack(fill=tk.X, pady=(10, 0))

        tk.Label(detection_frame, text="Zobrazenie:", font=('Arial', 10, 'bold'), bg='#f3f4f6').pack(side=tk.LEFT, padx=(0, 10))

        contours_check = tk.Checkbutton(detection_frame, text="Kontúry", variable=self.show_contours,
                                       command=self.toggle_contours, bg='#f3f4f6', font=('Arial', 9))
        contours_check.pack(side=tk.LEFT, padx=(0, 10))

        edges_check = tk.Checkbutton(detection_frame, text="Hrany", variable=self.show_edges,
                                    command=self.toggle_edges, bg='#f3f4f6', font=('Arial', 9))
        edges_check.pack(side=tk.LEFT, padx=(0, 10))

        detect_btn = tk.Button(detection_frame, text="Detekcia striech", command=self.detect_roofs,
                              bg='#7c3aed', fg='white', font=('Arial', 10, 'bold'),
                              padx=15, pady=3)
        detect_btn.pack(side=tk.LEFT, padx=(10, 0))

        contour_btn = tk.Button(detection_frame, text="Zobraziť kontúry", command=self.show_contour_analysis,
                               bg='#0891b2', fg='white', font=('Arial', 10, 'bold'),
                               padx=15, pady=3)
        contour_btn.pack(side=tk.LEFT, padx=(10, 0))

        # Canvas frame
        canvas_frame = tk.Frame(main_frame, bg='#e5e7eb', relief=tk.RAISED, bd=2)
        canvas_frame.pack(pady=(0, 20))

        self.canvas = tk.Canvas(canvas_frame, width=self.canvas_width, height=self.canvas_height,
                               bg='#e5e7eb', cursor='crosshair')
        self.canvas.pack(padx=10, pady=10)
        self.canvas.bind('<Button-1>', self.handle_canvas_click)

        # Control buttons frame
        controls_frame = tk.Frame(main_frame, bg='#f3f4f6')
        controls_frame.pack(fill=tk.X)

        # Left side buttons
        left_buttons = tk.Frame(controls_frame, bg='#f3f4f6')
        left_buttons.pack(side=tk.LEFT)

        self.auto_analyze_btn = tk.Button(left_buttons, text="Automatická Analýza",
                                         command=self.handle_auto_analyze,
                                         bg='#059669', fg='white', font=('Arial', 11, 'bold'),
                                         padx=15, pady=5, state=tk.DISABLED)
        self.auto_analyze_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.undo_btn = tk.Button(left_buttons, text="Späť", command=self.handle_undo,
                                 bg='#eab308', fg='white', font=('Arial', 11, 'bold'),
                                 padx=15, pady=5, state=tk.DISABLED)
        self.undo_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.reset_btn = tk.Button(left_buttons, text="Resetovať", command=self.handle_reset,
                                  bg='#dc2626', fg='white', font=('Arial', 11, 'bold'),
                                  padx=15, pady=5, state=tk.DISABLED)
        self.reset_btn.pack(side=tk.LEFT)

        # Right side - results
        results_frame = tk.Frame(controls_frame, bg='#f3f4f6')
        results_frame.pack(side=tk.RIGHT)

        result_label = tk.Label(results_frame, text="Plocha strechy:",
                               font=('Arial', 14, 'bold'), bg='#f3f4f6', fg='#1f2937')
        result_label.pack()

        self.area_label = tk.Label(results_frame, text="0.00 m²",
                                  font=('Arial', 18, 'bold'), bg='#f3f4f6', fg='#2563eb')
        self.area_label.pack()

        # Status bar
        self.status_var = tk.StringVar(value="Pripravený")
        status_bar = tk.Label(main_frame, textvariable=self.status_var,
                             font=('Arial', 9), bg='#e5e7eb', fg='#374151',
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, pady=(10, 0))

        # Bind Enter key to search
        self.address_entry.bind('<Return>', lambda e: self.handle_search())

        # Menu bar
        self.create_menu()

    def create_menu(self):
        """Create application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Súbor", menu=file_menu)
        file_menu.add_command(label="Exportovať výsledky...", command=self.export_results)
        file_menu.add_command(label="Uložiť projekt...", command=self.save_project)
        file_menu.add_command(label="Načítať projekt...", command=self.load_project)
        file_menu.add_separator()
        file_menu.add_command(label="Ukončiť", command=self.root.quit)

        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Nastavenia", menu=settings_menu)
        settings_menu.add_command(label="API kľúč...", command=self.configure_api_key)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Pomoc", menu=help_menu)
        help_menu.add_command(label="O aplikácii", command=self.show_about)

    def on_zoom_change(self, value):
        """Handle zoom level change"""
        self.current_zoom = int(value)
        if self.current_lat and self.current_lng:
            self.refresh_map()

    def on_map_type_change(self, event):
        """Handle map type change"""
        self.map_type = self.map_type_var.get()
        if self.current_lat and self.current_lng:
            self.refresh_map()

    def refresh_map(self):
        """Refresh the current map"""
        if self.current_lat and self.current_lng:
            self.load_satellite_image_from_coords(self.current_lat, self.current_lng)

    def toggle_contours(self):
        """Toggle contour display"""
        self.redraw_canvas()

    def toggle_edges(self):
        """Toggle edge display"""
        if self.show_edges.get() and not hasattr(self, 'edge_overlay'):
            self.create_edge_overlay()
        self.redraw_canvas()

    def create_edge_overlay(self):
        """Create edge detection overlay"""
        if not self.current_image or not OPENCV_AVAILABLE:
            return

        try:
            # Convert PIL image to OpenCV format
            img_array = np.array(self.current_image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Convert to grayscale
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Edge detection using Canny
            edges = cv2.Canny(blurred, 50, 150)

            # Convert edges to RGBA for overlay
            h, w = edges.shape
            edge_overlay = np.zeros((h, w, 4), dtype=np.uint8)

            # Set edge pixels to white with alpha
            edge_mask = edges > 0
            edge_overlay[edge_mask] = [255, 255, 255, 200]  # White with alpha

            # Convert to PIL format
            self.edge_overlay = Image.fromarray(edge_overlay, 'RGBA')

        except Exception as e:
            print(f"Chyba pri vytváraní edge overlay: {e}")
            self.edge_overlay = None

    def detect_roofs(self):
        """Detect roofs in the current image"""
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            return

        if not OPENCV_AVAILABLE:
            messagebox.showerror("Chyba", "OpenCV nie je nainštalované. Nainštalujte ho príkazom: pip install opencv-python")
            return

        self.status_var.set("Detekujem strechy...")
        threading.Thread(target=self.perform_roof_detection, daemon=True).start()

    def show_contour_analysis(self):
        """Show detailed contour analysis window"""
        if not self.detected_contours:
            messagebox.showwarning("Upozornenie", "Najprv spustite detekciu striech.")
            return

        if not MATPLOTLIB_AVAILABLE:
            messagebox.showerror("Chyba", "matplotlib nie je nainštalované. Nainštalujte ho príkazom: pip install matplotlib")
            return

        self.create_contour_analysis_window()

    def handle_search(self):
        address = self.address_var.get().strip()
        if not address or address == "Zadajte adresu (napr. Bratislava, Slovensko)":
            messagebox.showwarning("Upozornenie", "Prosím, zadajte adresu.")
            return

        if not self.api_key:
            messagebox.showerror("Chyba", "Google Maps API kľúč nie je nakonfigurovaný!")
            self.configure_api_key()
            return

        # Start geocoding in separate thread
        self.status_var.set("Vyhľadávam adresu...")
        threading.Thread(target=self.geocode_address, args=(address,), daemon=True).start()

    def geocode_address(self, address: str):
        """Geocode address using Google Maps Geocoding API"""
        try:
            # Check cache first
            if address in self.geocoding_cache:
                lat, lng = self.geocoding_cache[address]
                self.root.after(0, lambda: self.load_satellite_image_from_coords(lat, lng))
                return

            # Prepare geocoding request
            base_url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                'address': address,
                'key': self.api_key
            }

            response = requests.get(base_url, params=params, timeout=10)
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                location = data['results'][0]['geometry']['location']
                lat, lng = location['lat'], location['lng']

                # Cache the result
                self.geocoding_cache[address] = (lat, lng)

                # Load satellite image on main thread
                self.root.after(0, lambda: self.load_satellite_image_from_coords(lat, lng))

            else:
                error_msg = f"Geokódovanie zlyhalo: {data.get('status', 'Neznáma chyba')}"
                self.root.after(0, lambda: self.show_error(error_msg))

        except requests.RequestException as e:
            error_msg = f"Chyba siete: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))
        except Exception as e:
            error_msg = f"Neočakávaná chyba: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def load_satellite_image_from_coords(self, lat: float, lng: float):
        """Load satellite image from coordinates using Google Maps Static API"""
        try:
            self.status_var.set("Načítavam satelitnú mapu...")
            self.current_lat = lat
            self.current_lng = lng

            # Calculate meters per pixel based on zoom level
            self.calculate_meters_per_pixel(lat, self.current_zoom)

            # Prepare Static Maps API request
            base_url = "https://maps.googleapis.com/maps/api/staticmap"
            params = {
                'center': f"{lat},{lng}",
                'zoom': self.current_zoom,
                'size': f"{self.canvas_width}x{self.canvas_height}",
                'maptype': self.map_type,
                'key': self.api_key,
                'format': 'png'
            }

            # Start download in separate thread
            threading.Thread(target=self.download_map_image, args=(base_url, params), daemon=True).start()

        except Exception as e:
            self.show_error(f"Chyba pri načítavaní mapy: {str(e)}")

    def download_map_image(self, base_url: str, params: dict):
        """Download map image in separate thread"""
        try:
            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()

            # Load image
            img = Image.open(io.BytesIO(response.content))

            # Update UI on main thread
            self.root.after(0, lambda: self.display_map_image(img))

        except requests.RequestException as e:
            error_msg = f"Chyba pri sťahovaní mapy: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))
        except Exception as e:
            error_msg = f"Chyba pri spracovaní obrázka: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def display_map_image(self, img: Image.Image):
        """Display the downloaded map image"""
        try:
            self.current_image = img
            self.photo_image = ImageTk.PhotoImage(img)

            # Clear canvas and display image
            self.canvas.delete("all")
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)

            # Enable auto analyze button
            self.auto_analyze_btn.config(state=tk.NORMAL)

            # Reset points
            self.handle_reset()

            self.status_var.set(f"Mapa načítaná - Zoom: {self.current_zoom}, Typ: {self.map_type}")

        except Exception as e:
            self.show_error(f"Chyba pri zobrazovaní mapy: {str(e)}")

    def calculate_meters_per_pixel(self, lat: float, zoom: int):
        """Calculate meters per pixel for given latitude and zoom level"""
        # Earth's circumference at equator in meters
        earth_circumference = 40075016.686

        # Calculate meters per pixel
        # Formula: (cos(lat * π/180) * earth_circumference) / (256 * 2^zoom)
        lat_rad = math.radians(lat)
        self.meters_per_pixel = (math.cos(lat_rad) * earth_circumference) / (256 * (2 ** zoom))

    def show_error(self, message: str):
        """Show error message and update status"""
        messagebox.showerror("Chyba", message)
        self.status_var.set("Chyba")

    def perform_roof_detection(self):
        """Perform advanced roof detection using OpenCV"""
        try:
            # Convert PIL image to OpenCV format
            img_array = np.array(self.current_image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Multiple detection methods
            contours_edge = self.detect_roofs_by_edges(img_cv)
            contours_color = self.detect_roofs_by_color(img_cv)
            contours_watershed = self.detect_roofs_by_watershed(img_cv)

            # Combine and filter contours
            all_contours = contours_edge + contours_color + contours_watershed
            filtered_contours = self.filter_and_merge_contours(all_contours, img_cv.shape)

            # Update UI on main thread
            self.root.after(0, lambda: self.display_detected_contours(filtered_contours, img_cv))

        except Exception as e:
            error_msg = f"Chyba pri detekcii striech: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def detect_roofs_by_edges(self, img_cv):
        """Detect roofs using edge detection"""
        # Convert to grayscale
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Edge detection using Canny
        edges = cv2.Canny(blurred, 50, 150)

        # Morphological operations to close gaps
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        edges = cv2.dilate(edges, kernel, iterations=1)

        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        return contours

    def detect_roofs_by_color(self, img_cv):
        """Detect roofs using color segmentation"""
        # Convert to HSV for better color segmentation
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)

        # Define color ranges for typical roof colors
        roof_ranges = [
            # Red roofs
            ([0, 50, 50], [10, 255, 255]),
            ([170, 50, 50], [180, 255, 255]),
            # Brown/orange roofs
            ([10, 50, 50], [25, 255, 255]),
            # Gray roofs
            ([0, 0, 50], [180, 30, 200]),
            # Dark roofs
            ([0, 0, 0], [180, 255, 100])
        ]

        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)

        for lower, upper in roof_ranges:
            lower = np.array(lower, dtype=np.uint8)
            upper = np.array(upper, dtype=np.uint8)
            mask = cv2.inRange(hsv, lower, upper)
            combined_mask = cv2.bitwise_or(combined_mask, mask)

        # Morphological operations
        kernel = np.ones((5, 5), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)

        # Find contours
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        return contours

    def detect_roofs_by_watershed(self, img_cv):
        """Detect roofs using watershed segmentation"""
        # Convert to grayscale
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

        # Apply threshold
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # Noise removal
        kernel = np.ones((3, 3), np.uint8)
        opening = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel, iterations=2)

        # Sure background area
        sure_bg = cv2.dilate(opening, kernel, iterations=3)

        # Finding sure foreground area
        dist_transform = cv2.distanceTransform(opening, cv2.DIST_L2, 5)
        _, sure_fg = cv2.threshold(dist_transform, 0.7 * dist_transform.max(), 255, 0)

        # Finding unknown region
        sure_fg = np.uint8(sure_fg)
        unknown = cv2.subtract(sure_bg, sure_fg)

        # Marker labelling
        _, markers = cv2.connectedComponents(sure_fg)
        markers = markers + 1
        markers[unknown == 255] = 0

        # Apply watershed
        img_watershed = img_cv.copy()
        markers = cv2.watershed(img_watershed, markers)

        # Create mask from watershed result
        watershed_mask = np.zeros(gray.shape, dtype=np.uint8)
        watershed_mask[markers > 1] = 255

        # Find contours
        contours, _ = cv2.findContours(watershed_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        return contours

    def filter_and_merge_contours(self, contours, img_shape):
        """Filter and merge detected contours"""
        h, w = img_shape[:2]
        min_area = (w * h) * 0.001  # Minimum 0.1% of image area
        max_area = (w * h) * 0.3    # Maximum 30% of image area

        filtered_contours = []

        for contour in contours:
            area = cv2.contourArea(contour)

            # Filter by area
            if area < min_area or area > max_area:
                continue

            # Filter by aspect ratio and solidity
            x, y, w_rect, h_rect = cv2.boundingRect(contour)
            aspect_ratio = float(w_rect) / h_rect

            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = float(area) / hull_area if hull_area > 0 else 0

            # Keep contours that look like buildings
            if 0.2 < aspect_ratio < 5.0 and solidity > 0.3:
                # Approximate contour to reduce points
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) >= 3:  # At least triangle
                    filtered_contours.append(approx)

        # Remove overlapping contours
        final_contours = []
        for i, contour1 in enumerate(filtered_contours):
            is_duplicate = False
            for j, contour2 in enumerate(filtered_contours):
                if i != j:
                    # Check if contours overlap significantly
                    area1 = cv2.contourArea(contour1)
                    area2 = cv2.contourArea(contour2)

                    # Create masks and check intersection
                    mask1 = np.zeros(img_shape[:2], dtype=np.uint8)
                    mask2 = np.zeros(img_shape[:2], dtype=np.uint8)
                    cv2.fillPoly(mask1, [contour1], 255)
                    cv2.fillPoly(mask2, [contour2], 255)

                    intersection = cv2.bitwise_and(mask1, mask2)
                    intersection_area = cv2.countNonZero(intersection)

                    overlap_ratio = intersection_area / min(area1, area2) if min(area1, area2) > 0 else 0

                    if overlap_ratio > 0.5 and area2 > area1:
                        is_duplicate = True
                        break

            if not is_duplicate:
                final_contours.append(contour1)

        return final_contours[:10]  # Limit to 10 best contours

    def display_detected_contours(self, contours, img_cv):
        """Display detected contours on the canvas"""
        self.detected_contours = contours

        try:
            # Create overlay image for contours with alpha channel
            h, w = img_cv.shape[:2]
            overlay = np.zeros((h, w, 4), dtype=np.uint8)  # RGBA format

            # Draw contours with different colors
            colors = [
                (0, 255, 0, 180),    # Green with alpha
                (255, 0, 0, 180),    # Red with alpha
                (0, 0, 255, 180),    # Blue with alpha
                (255, 255, 0, 180),  # Yellow with alpha
                (255, 0, 255, 180),  # Magenta with alpha
                (0, 255, 255, 180),  # Cyan with alpha
                (128, 0, 128, 180),  # Purple with alpha
                (255, 165, 0, 180),  # Orange with alpha
                (255, 192, 203, 180), # Pink with alpha
                (0, 128, 0, 180)     # Dark Green with alpha
            ]

            for i, contour in enumerate(contours):
                color = colors[i % len(colors)]

                # Create temporary image for this contour
                temp_overlay = np.zeros((h, w, 3), dtype=np.uint8)
                cv2.drawContours(temp_overlay, [contour], -1, color[:3], 2)
                cv2.fillPoly(temp_overlay, [contour], color[:3])

                # Add to main overlay with alpha
                mask = np.any(temp_overlay > 0, axis=2)
                overlay[mask] = [color[0], color[1], color[2], color[3]]

                # Add contour number
                M = cv2.moments(contour)
                if M["m00"] != 0:
                    cx = int(M["m10"] / M["m00"])
                    cy = int(M["m01"] / M["m00"])
                    cv2.putText(overlay, str(i+1), (cx-10, cy+5), cv2.FONT_HERSHEY_SIMPLEX, 0.8,
                               (255, 255, 255, 255), 2)

            # Convert to PIL format
            self.contour_overlay = Image.fromarray(overlay, 'RGBA')

            # Redraw canvas with contours
            self.redraw_canvas()

            self.status_var.set(f"Detekcia dokončená - nájdených {len(contours)} striech")
            messagebox.showinfo("Detekcia striech", f"Nájdených {len(contours)} potenciálnych striech.\n\nKliknite na kontúru pre výber alebo použite 'Zobraziť kontúry' pre detailnú analýzu.")

        except Exception as e:
            self.status_var.set("Chyba pri zobrazovaní kontúr")
            messagebox.showerror("Chyba", f"Chyba pri zobrazovaní kontúr: {str(e)}")
            print(f"Detailná chyba: {e}")

    def handle_canvas_click(self, event):
        """Handle mouse clicks on the canvas to add points"""
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv vyhľadajte adresu.")
            return

        x = event.x
        y = event.y

        # Add point to the list
        self.points.append(Point(x, y))

        # Redraw the canvas
        self.redraw_canvas()

        # Update area calculation
        self.update_area()

    def redraw_canvas(self):
        """Redraw the canvas with current points and lines"""
        # Clear canvas
        self.canvas.delete("all")

        # Redraw background image if exists
        if self.photo_image:
            self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo_image)

        # Draw contour overlay if enabled and available
        if self.show_contours.get() and self.contour_overlay:
            # Create composite image with contours
            if self.current_image and self.contour_overlay:
                try:
                    # Ensure both images have the same mode and size
                    current_img = self.current_image.convert('RGBA')
                    contour_img = self.contour_overlay.convert('RGBA')

                    # Resize contour overlay to match current image if needed
                    if current_img.size != contour_img.size:
                        contour_img = contour_img.resize(current_img.size, Image.Resampling.LANCZOS)

                    # Blend images
                    blended = Image.alpha_composite(current_img, contour_img)
                    blended = blended.convert('RGB')  # Convert back to RGB for display

                    self.contour_photo = ImageTk.PhotoImage(blended)
                    self.canvas.create_image(0, 0, anchor=tk.NW, image=self.contour_photo)
                except Exception as e:
                    print(f"Chyba pri blendovaní: {e}")
                    # Fallback: just show contour overlay
                    contour_photo = ImageTk.PhotoImage(self.contour_overlay)
                    self.canvas.create_image(0, 0, anchor=tk.NW, image=contour_photo)

        # Draw edge overlay if enabled
        if self.show_edges.get() and hasattr(self, 'edge_overlay') and self.edge_overlay:
            try:
                edge_photo = ImageTk.PhotoImage(self.edge_overlay)
                self.canvas.create_image(0, 0, anchor=tk.NW, image=edge_photo)
            except Exception as e:
                print(f"Chyba pri zobrazovaní hrán: {e}")

        # Draw detected contours as clickable areas
        if self.show_contours.get() and self.detected_contours:
            self.draw_contour_boundaries()

        # Draw manual points and lines
        if len(self.points) > 0:
            # Draw lines between points
            if len(self.points) > 1:
                for i in range(len(self.points)):
                    p1 = self.points[i]
                    p2 = self.points[(i + 1) % len(self.points)]

                    if i == len(self.points) - 1 and len(self.points) < 3:
                        # Don't close the polygon if we have less than 3 points
                        break

                    self.canvas.create_line(p1.x, p1.y, p2.x, p2.y,
                                           fill='#00FF00', width=3, capstyle=tk.ROUND)

                    # Draw side length labels
                    if len(self.points) >= 3 or i < len(self.points) - 1:
                        self.draw_side_length(p1, p2)

            # Fill polygon if we have 3 or more points
            if len(self.points) >= 3:
                coords = []
                for point in self.points:
                    coords.extend([point.x, point.y])
                # Tkinter doesn't support alpha in hex colors, so we use stipple for transparency effect
                self.canvas.create_polygon(coords, fill='#00FF00', outline='#00FF00', width=3, stipple='gray25')

            # Draw points
            for point in self.points:
                self.canvas.create_oval(point.x - 5, point.y - 5, point.x + 5, point.y + 5,
                                       fill='#00FF00', outline='#008000', width=2)

    def draw_contour_boundaries(self):
        """Draw contour boundaries on canvas"""
        colors = ['#FF0000', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
                 '#800080', '#FFA500', '#FFC0CB', '#008000', '#800000']

        for i, contour in enumerate(self.detected_contours):
            color = colors[i % len(colors)]

            # Convert contour points to canvas coordinates
            points = []
            for point in contour:
                x, y = point[0]
                points.extend([x, y])

            if len(points) >= 6:  # At least 3 points (x,y pairs)
                # Draw contour outline
                self.canvas.create_polygon(points, fill='', outline=color, width=2, tags=f"contour_{i}")

                # Add clickable area
                self.canvas.tag_bind(f"contour_{i}", "<Button-1>", lambda e, idx=i: self.select_contour(idx))

                # Add contour label
                if len(contour) > 0:
                    # Calculate centroid
                    cx = sum(point[0][0] for point in contour) / len(contour)
                    cy = sum(point[0][1] for point in contour) / len(contour)

                    self.canvas.create_text(cx, cy, text=str(i+1), fill=color,
                                          font=('Arial', 12, 'bold'), tags=f"contour_{i}")

    def select_contour(self, contour_index):
        """Select a detected contour as the roof outline"""
        if contour_index < len(self.detected_contours):
            contour = self.detected_contours[contour_index]

            # Convert contour to Point objects
            self.points = []
            for point in contour:
                x, y = point[0]
                self.points.append(Point(x, y))

            # Redraw canvas and update area
            self.redraw_canvas()
            self.update_area()

            self.status_var.set(f"Vybratá kontúra {contour_index + 1}")
            messagebox.showinfo("Kontúra vybratá", f"Kontúra {contour_index + 1} bola vybratá ako obrys strechy.")

    def create_contour_analysis_window(self):
        """Create detailed contour analysis window"""
        analysis_window = tk.Toplevel(self.root)
        analysis_window.title("Analýza kontúr striech")
        analysis_window.geometry("800x600")
        analysis_window.configure(bg='#f3f4f6')

        # Create notebook for tabs
        notebook = ttk.Notebook(analysis_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 1: Visual analysis
        visual_frame = ttk.Frame(notebook)
        notebook.add(visual_frame, text="Vizuálna analýza")

        if MATPLOTLIB_AVAILABLE:
            self.create_visual_analysis_tab(visual_frame)
        else:
            tk.Label(visual_frame, text="matplotlib nie je nainštalované.\nNainštalujte ho pre vizuálnu analýzu.",
                    font=('Arial', 12), bg='#f3f4f6').pack(pady=50)

        # Tab 2: Statistical analysis
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Štatistická analýza")
        self.create_statistical_analysis_tab(stats_frame)

        # Tab 3: Contour details
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="Detaily kontúr")
        self.create_contour_details_tab(details_frame)

    def create_visual_analysis_tab(self, parent):
        """Create visual analysis tab with matplotlib"""
        try:
            # Create matplotlib figure
            fig, axes = plt.subplots(2, 2, figsize=(10, 8))
            fig.suptitle('Analýza detekcie striech', fontsize=14)

            # Convert current image to numpy array
            img_array = np.array(self.current_image)

            # Original image
            axes[0, 0].imshow(img_array)
            axes[0, 0].set_title('Originálny obrázok')
            axes[0, 0].axis('off')

            # Edge detection visualization
            if OPENCV_AVAILABLE:
                gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
                edges = cv2.Canny(gray, 50, 150)
                axes[0, 1].imshow(edges, cmap='gray')
                axes[0, 1].set_title('Detekcia hrán')
                axes[0, 1].axis('off')

            # Contours overlay
            if self.contour_overlay:
                contour_array = np.array(self.contour_overlay)
                axes[1, 0].imshow(contour_array)
                axes[1, 0].set_title('Detekované kontúry')
                axes[1, 0].axis('off')

            # Combined view
            if self.contour_overlay:
                blended = Image.blend(self.current_image, self.contour_overlay, 0.5)
                blended_array = np.array(blended)
                axes[1, 1].imshow(blended_array)
                axes[1, 1].set_title('Kombinovaný pohľad')
                axes[1, 1].axis('off')

            plt.tight_layout()

            # Embed matplotlib in tkinter
            canvas_widget = FigureCanvasTkAgg(fig, parent)
            canvas_widget.draw()
            canvas_widget.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            tk.Label(parent, text=f"Chyba pri vytváraní vizualizácie: {str(e)}",
                    font=('Arial', 10), bg='#f3f4f6', fg='red').pack(pady=20)

    def create_statistical_analysis_tab(self, parent):
        """Create statistical analysis tab"""
        # Create scrollable frame
        canvas = tk.Canvas(parent, bg='#f3f4f6')
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Statistics content
        tk.Label(scrollable_frame, text="Štatistická analýza detekovaných striech",
                font=('Arial', 14, 'bold')).pack(pady=10)

        if self.detected_contours:
            # Overall statistics
            total_contours = len(self.detected_contours)
            tk.Label(scrollable_frame, text=f"Celkový počet detekovaných kontúr: {total_contours}",
                    font=('Arial', 12)).pack(pady=5)

            # Individual contour statistics
            for i, contour in enumerate(self.detected_contours):
                frame = tk.LabelFrame(scrollable_frame, text=f"Kontúra {i+1}",
                                     font=('Arial', 11, 'bold'), padx=10, pady=5)
                frame.pack(fill=tk.X, padx=10, pady=5)

                if OPENCV_AVAILABLE:
                    # Calculate contour properties
                    area_pixels = cv2.contourArea(contour)
                    area_meters = area_pixels * (self.meters_per_pixel ** 2)
                    perimeter_pixels = cv2.arcLength(contour, True)
                    perimeter_meters = perimeter_pixels * self.meters_per_pixel

                    # Bounding rectangle
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    # Convex hull
                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = float(area_pixels) / hull_area if hull_area > 0 else 0

                    # Display statistics
                    tk.Label(frame, text=f"Plocha: {area_meters:.2f} m² ({area_pixels:.0f} pixelov)",
                            font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Obvod: {perimeter_meters:.2f} m ({perimeter_pixels:.0f} pixelov)",
                            font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Pomer strán: {aspect_ratio:.2f}",
                            font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Kompaktnosť: {solidity:.2f}",
                            font=('Arial', 10)).pack(anchor='w')
                    tk.Label(frame, text=f"Počet bodov: {len(contour)}",
                            font=('Arial', 10)).pack(anchor='w')

                    # Select button
                    select_btn = tk.Button(frame, text="Vybrať túto kontúru",
                                          command=lambda idx=i: self.select_contour_from_analysis(idx),
                                          bg='#2563eb', fg='white', font=('Arial', 9))
                    select_btn.pack(pady=5)
        else:
            tk.Label(scrollable_frame, text="Žiadne kontúry na analýzu.",
                    font=('Arial', 12), fg='gray').pack(pady=20)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_contour_details_tab(self, parent):
        """Create contour details tab"""
        # Create text widget with scrollbar
        text_frame = tk.Frame(parent)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Courier', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # Generate detailed report
        report = self.generate_contour_report()
        text_widget.insert(tk.END, report)
        text_widget.config(state=tk.DISABLED)

        text_widget.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Export button
        export_btn = tk.Button(parent, text="Exportovať detailný report",
                              command=lambda: self.export_contour_report(report),
                              bg='#059669', fg='white', font=('Arial', 11, 'bold'))
        export_btn.pack(pady=10)

    def select_contour_from_analysis(self, contour_index):
        """Select contour from analysis window"""
        self.select_contour(contour_index)
        # Close analysis windows
        for widget in self.root.winfo_children():
            if isinstance(widget, tk.Toplevel) and "Analýza kontúr" in widget.title():
                widget.destroy()

    def generate_contour_report(self):
        """Generate detailed contour analysis report"""
        report = "DETAILNÝ REPORT ANALÝZY KONTÚR STRIECH\n"
        report += "=" * 50 + "\n\n"
        report += f"Dátum analýzy: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}\n"
        report += f"Adresa: {self.address_var.get()}\n"
        report += f"Súradnice: {self.current_lat:.6f}, {self.current_lng:.6f}\n"
        report += f"Zoom úroveň: {self.current_zoom}\n"
        report += f"Typ mapy: {self.map_type}\n"
        report += f"Meter/pixel pomer: {self.meters_per_pixel:.6f}\n\n"

        if self.detected_contours:
            report += f"DETEKOVANÉ KONTÚRY: {len(self.detected_contours)}\n"
            report += "-" * 30 + "\n\n"

            for i, contour in enumerate(self.detected_contours):
                report += f"KONTÚRA {i+1}:\n"

                if OPENCV_AVAILABLE:
                    # Calculate properties
                    area_pixels = cv2.contourArea(contour)
                    area_meters = area_pixels * (self.meters_per_pixel ** 2)
                    perimeter_pixels = cv2.arcLength(contour, True)
                    perimeter_meters = perimeter_pixels * self.meters_per_pixel

                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = float(w) / h

                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = float(area_pixels) / hull_area if hull_area > 0 else 0

                    report += f"  Plocha: {area_meters:.2f} m² ({area_pixels:.0f} px)\n"
                    report += f"  Obvod: {perimeter_meters:.2f} m ({perimeter_pixels:.0f} px)\n"
                    report += f"  Ohraničujúci obdĺžnik: {w}x{h} px na pozícii ({x}, {y})\n"
                    report += f"  Pomer strán: {aspect_ratio:.3f}\n"
                    report += f"  Kompaktnosť (solidity): {solidity:.3f}\n"
                    report += f"  Počet bodov kontúry: {len(contour)}\n"

                    # Contour points
                    report += f"  Body kontúry (pixel súradnice):\n"
                    for j, point in enumerate(contour):
                        x_pt, y_pt = point[0]
                        report += f"    {j+1:2d}. ({x_pt:4.0f}, {y_pt:4.0f})\n"

                report += "\n"
        else:
            report += "Žiadne kontúry neboli detekované.\n"

        return report

    def export_contour_report(self, report):
        """Export contour analysis report"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Exportovať report analýzy kontúr"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                messagebox.showinfo("Export úspešný", f"Report bol exportovaný do:\n{filename}")
            except Exception as e:
                messagebox.showerror("Chyba exportu", f"Chyba pri exporte: {str(e)}")

    def draw_side_length(self, p1: Point, p2: Point):
        """Draw the length of a side between two points"""
        dx = p2.x - p1.x
        dy = p2.y - p1.y
        pixel_distance = math.sqrt(dx * dx + dy * dy)
        real_distance = pixel_distance * self.meters_per_pixel

        if real_distance < 1:
            return

        # Calculate midpoint
        mid_x = p1.x + dx / 2
        mid_y = p1.y + dy / 2

        # Create text
        text = f"{real_distance:.1f} m"

        # Draw background rectangle for text
        self.canvas.create_rectangle(mid_x - 25, mid_y - 8, mid_x + 25, mid_y + 8,
                                    fill='black', outline='black')

        # Draw text
        self.canvas.create_text(mid_x, mid_y, text=text, fill='white',
                               font=('Arial', 10, 'bold'))

    def calculate_polygon_area(self, vertices: List[Point]) -> float:
        """Calculate the area of a polygon using the shoelace formula"""
        if len(vertices) < 3:
            return 0

        area = 0
        j = len(vertices) - 1

        for i in range(len(vertices)):
            area += (vertices[j].x + vertices[i].x) * (vertices[j].y - vertices[i].y)
            j = i

        return abs(area / 2)

    def update_area(self):
        """Update the area display and button states"""
        # Update button states
        self.undo_btn.config(state=tk.NORMAL if len(self.points) > 0 else tk.DISABLED)
        self.reset_btn.config(state=tk.NORMAL if len(self.points) > 0 else tk.DISABLED)

        if len(self.points) < 3:
            self.area_label.config(text="0.00 m²")
            return

        # Calculate area
        pixel_area = self.calculate_polygon_area(self.points)
        real_area = pixel_area * (self.meters_per_pixel * self.meters_per_pixel)

        self.area_label.config(text=f"{real_area:.2f} m²")

    def handle_reset(self):
        """Reset all points and redraw canvas"""
        self.points = []
        self.redraw_canvas()
        self.update_area()

    def handle_undo(self):
        """Remove the last point and redraw"""
        if self.points:
            self.points.pop()
            self.redraw_canvas()
            self.update_area()

    def handle_auto_analyze(self):
        """Advanced automatic roof analysis using edge detection"""
        if not self.current_image:
            messagebox.showwarning("Upozornenie", "Najprv načítajte mapu.")
            return

        # Show analysis options dialog
        self.show_analysis_options()

    def show_analysis_options(self):
        """Show automatic analysis options dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Automatická analýza strechy")
        dialog.geometry("400x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        tk.Label(dialog, text="Automatická analýza strechy",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=10)

        tk.Label(dialog, text="Vyberte typ analýzy:",
                font=('Arial', 12), bg='#f3f4f6').pack(pady=10)

        analysis_var = tk.StringVar(value="edge_detection")

        tk.Radiobutton(dialog, text="Detekcia hrán (Edge Detection)",
                      variable=analysis_var, value="edge_detection",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=5)

        tk.Radiobutton(dialog, text="Detekcia farieb (Color Detection)",
                      variable=analysis_var, value="color_detection",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=5)

        tk.Radiobutton(dialog, text="Kombinovaná analýza",
                      variable=analysis_var, value="combined",
                      bg='#f3f4f6', font=('Arial', 10)).pack(pady=5)

        # Sensitivity settings
        tk.Label(dialog, text="Citlivosť detekcie:",
                font=('Arial', 11), bg='#f3f4f6').pack(pady=(20, 5))

        sensitivity_var = tk.DoubleVar(value=0.5)
        sensitivity_scale = tk.Scale(dialog, from_=0.1, to=1.0, resolution=0.1,
                                   orient=tk.HORIZONTAL, variable=sensitivity_var,
                                   bg='#f3f4f6', length=200)
        sensitivity_scale.pack(pady=5)

        def start_analysis():
            analysis_type = analysis_var.get()
            sensitivity = sensitivity_var.get()
            dialog.destroy()
            self.perform_auto_analysis(analysis_type, sensitivity)

        tk.Button(dialog, text="Spustiť analýzu", command=start_analysis,
                 bg='#059669', fg='white', font=('Arial', 12, 'bold'),
                 padx=20, pady=5).pack(pady=20)

        tk.Button(dialog, text="Zrušiť", command=dialog.destroy,
                 bg='#6b7280', fg='white', font=('Arial', 11),
                 padx=20, pady=5).pack(pady=5)

    def perform_auto_analysis(self, analysis_type: str, sensitivity: float):
        """Perform automatic roof analysis"""
        self.auto_analyze_btn.config(state=tk.DISABLED, text="Analyzujem...")
        self.status_var.set("Spúšťam automatickú analýzu...")

        # Start analysis in separate thread
        threading.Thread(target=self.run_analysis, args=(analysis_type, sensitivity), daemon=True).start()

    def run_analysis(self, analysis_type: str, sensitivity: float):
        """Run the actual analysis algorithm"""
        try:
            # Convert PIL image to format suitable for analysis
            import numpy as np

            # Convert image to numpy array
            img_array = np.array(self.current_image)

            # Simple edge detection algorithm
            if analysis_type == "edge_detection":
                points = self.detect_edges(img_array, sensitivity)
            elif analysis_type == "color_detection":
                points = self.detect_by_color(img_array, sensitivity)
            else:  # combined
                edge_points = self.detect_edges(img_array, sensitivity * 0.7)
                color_points = self.detect_by_color(img_array, sensitivity * 0.8)
                points = self.combine_detections(edge_points, color_points)

            # Update UI on main thread
            self.root.after(0, lambda: self.complete_auto_analysis(points))

        except Exception as e:
            error_msg = f"Chyba pri automatickej analýze: {str(e)}"
            self.root.after(0, lambda: self.show_error(error_msg))

    def detect_edges(self, img_array, sensitivity: float):
        """Simple edge detection algorithm"""
        try:
            import numpy as np

            # Convert to grayscale
            gray = np.dot(img_array[...,:3], [0.2989, 0.5870, 0.1140])

            # Simple edge detection using gradient
            grad_x = np.gradient(gray, axis=1)
            grad_y = np.gradient(gray, axis=0)
            magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # Find strong edges
            threshold = np.max(magnitude) * sensitivity
            edges = magnitude > threshold

            # Find contour points (simplified)
            points = []
            h, w = edges.shape

            # Sample points from edges
            for y in range(0, h, 20):
                for x in range(0, w, 20):
                    if edges[y, x]:
                        points.append(Point(x, y))

            # If we found points, try to create a polygon
            if len(points) > 3:
                # Sort points to form a rough polygon
                center_x = sum(p.x for p in points) / len(points)
                center_y = sum(p.y for p in points) / len(points)

                # Sort by angle from center
                def angle_from_center(point):
                    return math.atan2(point.y - center_y, point.x - center_x)

                points.sort(key=angle_from_center)

                # Take only a reasonable number of points
                if len(points) > 12:
                    step = len(points) // 12
                    points = points[::step]

            return points[:12]  # Limit to 12 points max

        except ImportError:
            # Fallback if numpy is not available
            return self.simple_fallback_detection()
        except Exception:
            return self.simple_fallback_detection()

    def detect_by_color(self, img_array, sensitivity: float):
        """Color-based detection algorithm"""
        # Simplified color detection - look for roof-like colors
        try:
            import numpy as np

            # Define roof color ranges (simplified)
            roof_colors = [
                ([100, 50, 50], [150, 100, 100]),    # Reddish
                ([80, 80, 80], [120, 120, 120]),     # Grayish
                ([60, 40, 20], [100, 80, 60])        # Brownish
            ]

            points = []
            h, w, _ = img_array.shape

            for color_range in roof_colors:
                lower, upper = color_range
                mask = np.all((img_array >= lower) & (img_array <= upper), axis=2)

                # Find points in this color range
                for y in range(0, h, 25):
                    for x in range(0, w, 25):
                        if mask[y, x]:
                            points.append(Point(x, y))

            # Process points similar to edge detection
            if len(points) > 3:
                center_x = sum(p.x for p in points) / len(points)
                center_y = sum(p.y for p in points) / len(points)

                def angle_from_center(point):
                    return math.atan2(point.y - center_y, point.x - center_x)

                points.sort(key=angle_from_center)

                if len(points) > 10:
                    step = len(points) // 10
                    points = points[::step]

            return points[:10]

        except ImportError:
            return self.simple_fallback_detection()
        except Exception:
            return self.simple_fallback_detection()

    def combine_detections(self, edge_points, color_points):
        """Combine edge and color detection results"""
        all_points = edge_points + color_points

        if not all_points:
            return self.simple_fallback_detection()

        # Remove duplicate points (within 20 pixels)
        unique_points = []
        for point in all_points:
            is_duplicate = False
            for existing in unique_points:
                distance = math.sqrt((point.x - existing.x)**2 + (point.y - existing.y)**2)
                if distance < 20:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_points.append(point)

        return unique_points[:12]

    def simple_fallback_detection(self):
        """Simple fallback detection when advanced algorithms fail"""
        # Create a simple rectangular detection in the center
        center_x = self.canvas_width // 2
        center_y = self.canvas_height // 2
        size = min(self.canvas_width, self.canvas_height) // 4

        return [
            Point(center_x - size, center_y - size),
            Point(center_x + size, center_y - size),
            Point(center_x + size, center_y + size),
            Point(center_x - size, center_y + size)
        ]

    def complete_auto_analysis(self, detected_points):
        """Complete the auto analysis with detected points"""
        if detected_points and len(detected_points) >= 3:
            self.points = detected_points
            self.redraw_canvas()
            self.update_area()

            self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza")
            self.status_var.set(f"Analýza dokončená - nájdených {len(detected_points)} bodov")
            messagebox.showinfo("Úspech", f"Automatická analýza dokončená!\nNájdených {len(detected_points)} bodov strechy.")
        else:
            self.auto_analyze_btn.config(state=tk.NORMAL, text="Automatická Analýza")
            self.status_var.set("Analýza neúspešná")
            messagebox.showwarning("Upozornenie", "Automatická analýza nenašla žiadne vhodné body strechy.\nSkúste manuálne označenie alebo zmeňte nastavenia.")

    def export_results(self):
        """Export analysis results to file"""
        if not self.points or len(self.points) < 3:
            messagebox.showwarning("Upozornenie", "Nie sú k dispozícii žiadne výsledky na export.")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")],
            title="Exportovať výsledky"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Analyzátor rozmerov strechy - Výsledky\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(f"Dátum: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}\n")
                    f.write(f"Adresa: {self.address_var.get()}\n")
                    f.write(f"Súradnice: {self.current_lat:.6f}, {self.current_lng:.6f}\n")
                    f.write(f"Zoom: {self.current_zoom}\n")
                    f.write(f"Typ mapy: {self.map_type}\n")
                    f.write(f"Metrový pomer: {self.meters_per_pixel:.6f} m/pixel\n\n")

                    # Calculate area
                    pixel_area = self.calculate_polygon_area(self.points)
                    real_area = pixel_area * (self.meters_per_pixel * self.meters_per_pixel)

                    f.write(f"Plocha strechy: {real_area:.2f} m²\n")
                    f.write(f"Počet bodov: {len(self.points)}\n\n")

                    f.write("Body polygónu (pixel súradnice):\n")
                    for i, point in enumerate(self.points, 1):
                        f.write(f"{i:2d}. X: {point.x:6.1f}, Y: {point.y:6.1f}\n")

                    f.write("\nDĺžky strán:\n")
                    for i in range(len(self.points)):
                        p1 = self.points[i]
                        p2 = self.points[(i + 1) % len(self.points)]

                        dx = p2.x - p1.x
                        dy = p2.y - p1.y
                        pixel_distance = math.sqrt(dx * dx + dy * dy)
                        real_distance = pixel_distance * self.meters_per_pixel

                        f.write(f"Strana {i+1}-{((i+1) % len(self.points))+1}: {real_distance:.2f} m\n")

                messagebox.showinfo("Úspech", f"Výsledky boli exportované do súboru:\n{filename}")

            except Exception as e:
                messagebox.showerror("Chyba",
















































                                     f"Chyba pri exporte: {str(e)}")

    def save_project(self):
        """Save current project to file"""
        if not self.points:
            messagebox.showwarning("Upozornenie", "Nie je čo uložiť.")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Uložiť projekt"
        )

        if filename:
            try:
                project_data = {
                    'version': '1.0',
                    'timestamp': datetime.now().isoformat(),
                    'address': self.address_var.get(),
                    'coordinates': {
                        'lat': self.current_lat,
                        'lng': self.current_lng
                    },
                    'map_settings': {
                        'zoom': self.current_zoom,
                        'map_type': self.map_type
                    },
                    'points': [{'x': p.x, 'y': p.y} for p in self.points],
                    'meters_per_pixel': self.meters_per_pixel
                }

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("Úspech", f"Projekt bol uložený do súboru:\n{filename}")

            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri ukladaní: {str(e)}")

    def load_project(self):
        """Load project from file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            title="Načítať projekt"
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # Restore project data
                if 'address' in project_data:
                    self.address_var.set(project_data['address'])

                if 'coordinates' in project_data:
                    coords = project_data['coordinates']
                    self.current_lat = coords['lat']
                    self.current_lng = coords['lng']

                if 'map_settings' in project_data:
                    settings = project_data['map_settings']
                    self.current_zoom = settings.get('zoom', 20)
                    self.map_type = settings.get('map_type', 'satellite')
                    self.zoom_var.set(self.current_zoom)
                    self.map_type_var.set(self.map_type)

                if 'meters_per_pixel' in project_data:
                    self.meters_per_pixel = project_data['meters_per_pixel']

                # Restore points
                if 'points' in project_data:
                    self.points = [Point(p['x'], p['y']) for p in project_data['points']]

                # Reload map and redraw
                if self.current_lat and self.current_lng:
                    self.load_satellite_image_from_coords(self.current_lat, self.current_lng)

                messagebox.showinfo("Úspech", f"Projekt bol načítaný zo súboru:\n{filename}")

            except Exception as e:
                messagebox.showerror("Chyba", f"Chyba pri načítavaní: {str(e)}")

    def show_about(self):
        """Show about dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("O aplikácii")
        dialog.geometry("400x300")
        dialog.configure(bg='#f3f4f6')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        tk.Label(dialog, text="Analyzátor rozmerov strechy",
                font=('Arial', 16, 'bold'), bg='#f3f4f6').pack(pady=20)

        tk.Label(dialog, text="Verzia 2.0",
                font=('Arial', 12), bg='#f3f4f6').pack(pady=5)

        tk.Label(dialog, text="Profesionálny nástroj na analýzu rozmerov striech\npoužívajúci Google Maps API",
                font=('Arial', 10), bg='#f3f4f6', justify=tk.CENTER).pack(pady=10)

        tk.Label(dialog, text="Funkcie:",
                font=('Arial', 11, 'bold'), bg='#f3f4f6').pack(pady=(20, 5))

        features = [
            "• Reálne satelitné mapy z Google Maps",
            "• Automatická detekcia striech",
            "• Presné meranie plôch a vzdialeností",
            "• Export výsledkov",
            "• Ukladanie a načítavanie projektov"
        ]

        for feature in features:
            tk.Label(dialog, text=feature, font=('Arial', 9), bg='#f3f4f6', anchor='w').pack(pady=1)

        tk.Button(dialog, text="Zavrieť", command=dialog.destroy,
                 bg='#2563eb', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5).pack(pady=20)

def main():
    root = tk.Tk()
    app = RoofAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()




