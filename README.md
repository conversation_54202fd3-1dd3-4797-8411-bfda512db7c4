# Analy<PERSON><PERSON><PERSON> r<PERSON> strechy - Google Maps

Profesionálny nástroj na analýzu rozmerov striech používajúci Google Maps API.

## Funkcie

- **Reálne satelitné mapy** - Používa Google Maps Static API pre načítanie skutočných satelitných snímok
- **Automatická detekcia striech** - Pokročilé algoritmy na automatické rozpoznávanie obrysov striech
- **Presné meranie** - Výpočet plôch a vzdialeností s vysokou presnosťou
- **Export výsledkov** - Export do TXT/CSV súborov
- **Ukladanie projektov** - Uloženie a načítanie projektov vo formáte JSON
- **Rôzne typy máp** - Satelitná, hybridná a cestná mapa
- **Nastaviteľný zoom** - Zoom úrovne 15-22 pre detailnú analýzu

## Inštalácia

### 1. Nainštalujte Python závislosti

```bash
pip install -r requirements.txt
```

### 2. Získajte Google Maps API kľúč

1. Idite na [Google Cloud Console](https://console.cloud.google.com/)
2. Vytvorte nový projekt alebo vyberte existujúci
3. Povoľte nasledujúce API:
   - **Maps Static API**
   - **Geocoding API**
4. Vytvorte API kľúč v sekcii "Credentials"
5. (Voliteľne) Obmedzte API kľúč na vaše IP adresy

### 3. Konfigurácia API kľúča

Máte tri možnosti:

**Možnosť A: Súbor api_key.txt**
```bash
echo "VÁŠ_API_KĽÚČ" > api_key.txt
```

**Možnosť B: Environment variable**
```bash
export GOOGLE_MAPS_API_KEY="VÁŠ_API_KĽÚČ"
```

**Možnosť C: Zadanie v aplikácii**
- Spustite aplikáciu a zadajte API kľúč v dialógu

## Spustenie

```bash
python main.py
```

## Použitie

### Základné použitie

1. **Zadajte adresu** - Napríklad "Bratislava, Slovensko"
2. **Kliknite "Hľadať"** - Načíta sa satelitná mapa
3. **Nastavte zoom a typ mapy** - Podľa potreby
4. **Označte obrys strechy**:
   - **Manuálne**: Klikajte na body okolo strechy
   - **Automaticky**: Použite "Automatická Analýza"
5. **Zobrazí sa plocha** - V pravom dolnom rohu

### Pokročilé funkcie

- **Undo/Reset** - Odstránenie posledného bodu alebo reset
- **Export výsledkov** - Súbor → Exportovať výsledky
- **Uloženie projektu** - Súbor → Uložiť projekt
- **Načítanie projektu** - Súbor → Načítať projekt

### Automatická analýza

Aplikácia ponúka tri typy automatickej analýzy:

1. **Detekcia hrán** - Hľadá ostré prechody v obrázku
2. **Detekcia farieb** - Hľadá typické farby striech
3. **Kombinovaná** - Kombinuje oba prístupy

## Technické detaily

### Presnosť merania

- Aplikácia automaticky vypočítava meter/pixel pomer na základe:
  - Geografickej šírky
  - Zoom úrovne
- Presnosť závisí od kvality satelitných snímok Google Maps

### Podporované formáty

- **Export**: TXT, CSV
- **Projekty**: JSON
- **Mapy**: PNG (z Google Maps Static API)

## Riešenie problémov

### API kľúč nefunguje
- Skontrolujte, či je API kľúč správny
- Overte, že sú povolené Maps Static API a Geocoding API
- Skontrolujte kvóty a limity v Google Cloud Console

### Automatická analýza nefunguje
- Skúste zmeniť citlivosť detekcie
- Použite vyšší zoom pre lepšie detaily
- Skúste iný typ mapy (satellite vs hybrid)
- Pri zlyhnutí použite manuálne označenie

### Chyby siete
- Skontrolujte internetové pripojenie
- Overte, že nie sú blokované požiadavky na maps.googleapis.com

## Licencia

Tento projekt je určený na vzdelávacie a komerčné účely.
Nezabudnite dodržiavať podmienky používania Google Maps API.

## Podpora

Pre technickú podporu alebo hlásenie chýb vytvorte issue v tomto repozitári.
